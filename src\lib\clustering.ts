import { prisma } from './prisma';
// @ts-ignore - haversine doesn't have types
import { haversine } from 'haversine';

export interface PhotoWithLocation {
  id: string;
  takenAt: Date;
  lat?: number;
  lng?: number;
  geohash?: string;
}

export interface Cluster {
  id: string;
  kind: 'trip' | 'home';
  startAt: Date;
  endAt: Date;
  centerLat?: number;
  centerLng?: number;
  label?: string;
  photoIds: string[];
}

export class ClusteringService {
  // Time gap threshold for new cluster (6 hours)
  private readonly TIME_GAP_MS = 6 * 60 * 60 * 1000;

  // Distance threshold for new cluster (100 km)
  private readonly DISTANCE_THRESHOLD_KM = 100;

  // Minimum trip duration for trip classification (1 day)
  private readonly MIN_TRIP_DURATION_MS = 24 * 60 * 60 * 1000;

  // Minimum distance for trip classification (50 km)
  private readonly MIN_TRIP_DISTANCE_KM = 50;

  async clusterPhotos(userId: string): Promise<Cluster[]> {
    // Get all photos for user, sorted by takenAt
    const photos = await prisma.photo.findMany({
      where: {
        userId,
        takenAt: { not: null },
      },
      select: {
        id: true,
        takenAt: true,
        lat: true,
        lng: true,
        geohash: true,
      },
      orderBy: { takenAt: 'asc' },
    });

    const validPhotos = photos.filter((p: any) => p.takenAt) as PhotoWithLocation[];
    const clusters: Cluster[] = [];

    if (validPhotos.length === 0) return clusters;

    let currentCluster: PhotoWithLocation[] = [validPhotos[0]];
    let currentStartTime = validPhotos[0].takenAt;

    for (let i = 1; i < validPhotos.length; i++) {
      const currentPhoto = validPhotos[i];
      const previousPhoto = validPhotos[i - 1];

      const timeGap = currentPhoto.takenAt.getTime() - previousPhoto.takenAt.getTime();
      const shouldStartNewCluster = this.shouldStartNewCluster(
        previousPhoto,
        currentPhoto,
        timeGap
      );

      if (shouldStartNewCluster) {
        // Create cluster from accumulated photos
        const cluster = await this.createCluster(currentCluster, userId);
        if (cluster) {
          clusters.push(cluster);
        }

        // Start new cluster
        currentCluster = [currentPhoto];
        currentStartTime = currentPhoto.takenAt;
      } else {
        // Add to current cluster
        currentCluster.push(currentPhoto);
      }
    }

    // Create final cluster
    const finalCluster = await this.createCluster(currentCluster, userId);
    if (finalCluster) {
      clusters.push(finalCluster);
    }

    return clusters;
  }

  private shouldStartNewCluster(
    photo1: PhotoWithLocation,
    photo2: PhotoWithLocation,
    timeGap: number
  ): boolean {
    // Always start new cluster if time gap is too large
    if (timeGap > this.TIME_GAP_MS) {
      return true;
    }

    // Check distance if both photos have location
    if (photo1.lat && photo1.lng && photo2.lat && photo2.lng) {
      const distance = haversine(
        { latitude: photo1.lat, longitude: photo1.lng },
        { latitude: photo2.lat, longitude: photo2.lng }
      );

      if (distance > this.DISTANCE_THRESHOLD_KM) {
        return true;
      }
    }

    return false;
  }

  private async createCluster(photos: PhotoWithLocation[], userId: string): Promise<Cluster | null> {
    if (photos.length === 0) return null;

    const startAt = photos[0].takenAt;
    const endAt = photos[photos.length - 1].takenAt;
    const duration = endAt.getTime() - startAt.getTime();

    // Calculate center point if photos have locations
    const photosWithLocation = photos.filter(p => p.lat && p.lng);
    let centerLat: number | undefined;
    let centerLng: number | undefined;

    if (photosWithLocation.length > 0) {
      centerLat = photosWithLocation.reduce((sum, p) => sum + p.lat!, 0) / photosWithLocation.length;
      centerLng = photosWithLocation.reduce((sum, p) => sum + p.lng!, 0) / photosWithLocation.length;
    }

    // Determine if this is a trip or home
    const kind = this.classifyCluster(photos, duration);

    // Generate label (simplified - in production, use reverse geocoding)
    const label = await this.generateLabel(photos, centerLat, centerLng);

    // Create cluster in database
    const cluster = await prisma.cluster.create({
      data: {
        userId,
        kind,
        startAt,
        endAt,
        centerLat,
        centerLng,
        label,
      },
    });

    // Update photos with cluster ID
    await prisma.photo.updateMany({
      where: {
        id: { in: photos.map(p => p.id) },
      },
      data: {
        clusterId: cluster.id,
      },
    });

    return {
      id: cluster.id,
      kind: cluster.kind as 'trip' | 'home',
      startAt: cluster.startAt,
      endAt: cluster.endAt,
      centerLat: cluster.centerLat || undefined,
      centerLng: cluster.centerLng || undefined,
      label: cluster.label || undefined,
      photoIds: photos.map(p => p.id),
    };
  }

  private classifyCluster(photos: PhotoWithLocation[], duration: number): 'trip' | 'home' {
    // Check duration requirement
    if (duration < this.MIN_TRIP_DURATION_MS) {
      return 'home';
    }

    // Check distance requirement
    const photosWithLocation = photos.filter(p => p.lat && p.lng);
    if (photosWithLocation.length < 2) {
      return 'home'; // Not enough location data to determine
    }

    // Calculate total distance traveled
    let totalDistance = 0;
    for (let i = 1; i < photosWithLocation.length; i++) {
      const p1 = photosWithLocation[i - 1];
      const p2 = photosWithLocation[i];

      totalDistance += haversine(
        { latitude: p1.lat!, longitude: p1.lng! },
        { latitude: p2.lat!, longitude: p2.lng! }
      );
    }

    return totalDistance > this.MIN_TRIP_DISTANCE_KM ? 'trip' : 'home';
  }

  private async generateLabel(
    photos: PhotoWithLocation[],
    centerLat?: number,
    centerLng?: number
  ): Promise<string | undefined> {
    // Simplified label generation
    // In production, you would use reverse geocoding service
    if (!centerLat || !centerLng) {
      return undefined;
    }

    // Mock reverse geocoding - replace with actual service
    // This is a placeholder that would be replaced with real geocoding
    return `Location (${centerLat.toFixed(2)}, ${centerLng.toFixed(2)})`;
  }

  async getHomeGeohash(userId: string): Promise<string | null> {
    // Find the most frequent geohash from the last 90 days
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

    const photos = await prisma.photo.findMany({
      where: {
        userId,
        takenAt: {
          gte: ninetyDaysAgo,
          not: null,
        },
        geohash: { not: null },
      },
      select: { geohash: true },
    });

    const geohashCounts = new Map<string, number>();
    for (const photo of photos) {
      if (photo.geohash) {
        geohashCounts.set(photo.geohash, (geohashCounts.get(photo.geohash) || 0) + 1);
      }
    }

    let mostFrequent: string | null = null;
    let maxCount = 0;

    for (const [geohash, count] of geohashCounts) {
      if (count > maxCount) {
        maxCount = count;
        mostFrequent = geohash;
      }
    }

    return mostFrequent;
  }
}
