"use client"

import { useState } from "react"
import { PhotoCard } from "./PhotoCard"
import { Button } from "@/components/ui/button"

interface Photo {
  id: string
  url: string
  thumbUrl: string
  takenAt: string | null
  quality: number | null
  significance: number | null
  cluster?: {
    kind: string
    label: string | null
  }
}

interface SignificanceBreakdown {
  quality: number
  people: number
  context: number
  subjectVariety: number
  social: number
  novelty: number
}

interface PhotoGridProps {
  photos: Photo[]
  scoreBreakdowns?: Record<string, SignificanceBreakdown>
  onFeedback?: (photoId: string, type: 'upvote' | 'downvote') => void
  showFeedback?: boolean
  loading?: boolean
  hasMore?: boolean
  onLoadMore?: () => void
  emptyMessage?: string
}

export function PhotoGrid({
  photos,
  scoreBreakdowns,
  onFeedback,
  showFeedback = false,
  loading = false,
  hasMore = false,
  onLoadMore,
  emptyMessage = "No photos found"
}: PhotoGridProps) {
  const [feedbackStatus, setFeedbackStatus] = useState<Record<string, 'upvote' | 'downvote' | null>>({})

  const handleFeedback = async (photoId: string, type: 'upvote' | 'downvote') => {
    if (feedbackStatus[photoId]) return // Already provided feedback

    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ photoId, type }),
      })

      if (response.ok) {
        setFeedbackStatus(prev => ({ ...prev, [photoId]: type }))
        if (onFeedback) {
          onFeedback(photoId, type)
        }
      } else {
        console.error('Failed to submit feedback')
      }
    } catch (error) {
      console.error('Error submitting feedback:', error)
    }
  }

  if (loading && photos.length === 0) {
    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {Array.from({ length: 20 }).map((_, i) => (
          <div key={i} className="aspect-square bg-gray-200 animate-pulse rounded-lg"></div>
        ))}
      </div>
    )
  }

  if (photos.length === 0) {
    return (
      <div className="text-center py-12">
        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
        </svg>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No photos yet</h3>
        <p className="text-gray-500">{emptyMessage}</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {photos.map((photo) => (
          <PhotoCard
            key={photo.id}
            photo={photo}
            scoreBreakdown={scoreBreakdowns?.[photo.id]}
            onFeedback={handleFeedback}
            showFeedback={showFeedback && !feedbackStatus[photo.id]}
          />
        ))}
      </div>

      {loading && photos.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {Array.from({ length: 10 }).map((_, i) => (
            <div key={i} className="aspect-square bg-gray-200 animate-pulse rounded-lg"></div>
          ))}
        </div>
      )}

      {hasMore && onLoadMore && (
        <div className="text-center">
          <Button
            onClick={onLoadMore}
            disabled={loading}
            variant="outline"
            className="px-8 py-2"
          >
            {loading ? 'Loading...' : 'Load More Photos'}
          </Button>
        </div>
      )}
    </div>
  )
}
