import { prisma } from './prisma';
import { ScoringService } from './scoring';
import { ClusteringService } from './clustering';

export interface Memory {
  id: string;
  clusterId: string;
  title: string;
  summary: string;
  coverPhotoId: string;
  startAt: Date;
  endAt: Date;
}

export class MemoryGenerationService {
  private scoringService = new ScoringService();
  private clusteringService = new ClusteringService();

  async generateMemories(userId: string): Promise<Memory[]> {
    // Get top 10 photos with diversity constraints
    const topPhotos = await this.scoringService.getDiverseTopPhotos(userId, 10);

    if (topPhotos.length === 0) return [];

    // Get unique clusters from top photos
    const clusterIds = new Set<string>();
    for (const score of topPhotos) {
      const photo = await prisma.photo.findUnique({
        where: { id: score.photoId },
        select: { clusterId: true },
      });
      if (photo?.clusterId) {
        clusterIds.add(photo.clusterId);
      }
    }

    // Generate memories for each cluster that has at least one top photo
    const memories: Memory[] = [];

    for (const clusterId of clusterIds) {
      const cluster = await prisma.cluster.findUnique({
        where: { id: clusterId },
        include: {
          photos: {
            orderBy: { significance: 'desc' },
          },
        },
      });

      if (!cluster) continue;

      // Find highest scoring photo in this cluster
      const topPhotosInCluster = topPhotos.filter(score =>
        cluster.photos.some(p => p.id === score.photoId)
      );

      if (topPhotosInCluster.length === 0) continue;

      // Use the highest scoring photo as cover
      const coverPhoto = topPhotosInCluster[0];
      const coverPhotoData = cluster.photos.find(p => p.id === coverPhoto.photoId);

      if (!coverPhotoData) continue;

      // Generate memory
      const memory = await this.generateMemory(cluster, coverPhotoData.id);
      memories.push(memory);
    }

    // Save memories to database
    const savedMemories = [];
    for (const memory of memories) {
      const saved = await prisma.memory.create({
        data: {
          userId,
          clusterId: memory.clusterId,
          title: memory.title,
          summary: memory.summary,
          coverPhotoId: memory.coverPhotoId,
          startAt: memory.startAt,
          endAt: memory.endAt,
        },
      });
      savedMemories.push(saved);
    }

    return savedMemories;
  }

  private async generateMemory(cluster: any, coverPhotoId: string): Promise<Memory> {
    // Generate title
    const title = await this.generateTitle(cluster);

    // Generate summary
    const summary = await this.generateSummary(cluster);

    return {
      id: '', // Will be set by database
      clusterId: cluster.id,
      title,
      summary,
      coverPhotoId,
      startAt: cluster.startAt,
      endAt: cluster.endAt,
    };
  }

  private async generateTitle(cluster: any): Promise<string> {
    const kind = cluster.kind;
    const hasLocation = cluster.centerLat && cluster.centerLng;

    if (kind === 'trip') {
      if (hasLocation && cluster.label) {
        // Extract city name from label (simplified)
        const locationName = cluster.label.includes('Location')
          ? cluster.label.replace('Location (', '').replace(')', '')
          : cluster.label;

        return `${this.getRandomTripPrefix()} in ${locationName}`;
      } else {
        return `${this.getRandomTripPrefix()} Away`;
      }
    } else {
      // Home cluster
      const month = new Date(cluster.startAt).toLocaleString('default', { month: 'long' });
      const year = new Date(cluster.startAt).getFullYear();
      return `${month} ${year} at Home`;
    }
  }

  private getRandomTripPrefix(): string {
    const prefixes = [
      'Weekend',
      'Week',
      'Day',
      'Trip',
      'Adventure',
      'Journey',
      'Holiday',
      'Vacation',
      'Visit',
    ];
    return prefixes[Math.floor(Math.random() * prefixes.length)];
  }

  private async generateSummary(cluster: any): Promise<string> {
    const kind = cluster.kind;
    const photoCount = cluster.photos.length;
    const duration = Math.ceil((new Date(cluster.endAt).getTime() - new Date(cluster.startAt).getTime()) / (1000 * 60 * 60 * 24));

    // Analyze photos for content hints
    const timeOfDay = this.analyzeTimeOfDay(cluster.photos);
    const activityHints = this.analyzeActivity(cluster.photos);

    let summary = '';

    if (kind === 'trip') {
      summary += `A ${duration}-day trip`;

      if (cluster.label) {
        const locationName = cluster.label.includes('Location')
          ? cluster.label.replace('Location (', '').replace(')', '')
          : cluster.label;
        summary += ` to ${locationName}`;
      }

      summary += ` with ${photoCount} photos.`;

      if (activityHints.length > 0) {
        summary += ` Highlights include ${activityHints.join(', ')}.`;
      }

      if (timeOfDay === 'day') {
        summary += ' Beautiful daytime shots capture the journey.';
      } else if (timeOfDay === 'night') {
        summary += ' Evening and night photos show the magical moments.';
      }
    } else {
      summary += `Photos from ${photoCount} moments at home over ${duration} day${duration > 1 ? 's' : ''}.`;

      if (activityHints.length > 0) {
        summary += ` Featuring ${activityHints.join(', ')}.`;
      }

      if (timeOfDay === 'day') {
        summary += ' Daytime activities and indoor moments.';
      } else if (timeOfDay === 'night') {
        summary += ' Evening relaxation and family time.';
      }
    }

    return summary;
  }

  private analyzeTimeOfDay(photos: any[]): 'day' | 'night' | 'mixed' {
    if (photos.length === 0) return 'mixed';

    const dayPhotos = photos.filter((p: any) => {
      if (!p.takenAt) return false;
      const hour = new Date(p.takenAt).getHours();
      return hour >= 6 && hour <= 18;
    });

    const dayRatio = dayPhotos.length / photos.length;

    if (dayRatio > 0.7) return 'day';
    if (dayRatio < 0.3) return 'night';
    return 'mixed';
  }

  private analyzeActivity(photos: any[]): string[] {
    const hints: string[] = [];

    // Analyze photo metadata for activity hints
    for (const photo of photos) {
      if (photo.quality && photo.quality > 80) {
        hints.push('high-quality moments');
      }

      // Check for people (if faces are detected)
      if (photo.faces && photo.faces > 0) {
        hints.push('people and connections');
      }

      // Check for location variety
      if (photo.lat && photo.lng) {
        hints.push('exploration and discovery');
      }
    }

    // Remove duplicates and limit to 3 hints
    const uniqueHints = [...new Set(hints)];
    return uniqueHints.slice(0, 3);
  }

  async regenerateMemory(userId: string, memoryId: string): Promise<Memory | null> {
    const memory = await prisma.memory.findFirst({
      where: { id: memoryId, userId },
      include: { cluster: { include: { photos: true } } },
    });

    if (!memory) return null;

    // Regenerate title and summary
    const newTitle = await this.generateTitle(memory.cluster);
    const newSummary = await this.generateSummary(memory.cluster);

    // Update in database
    const updated = await prisma.memory.update({
      where: { id: memoryId },
      data: {
        title: newTitle,
        summary: newSummary,
      },
    });

    return updated;
  }

  async getMemories(userId: string): Promise<Memory[]> {
    return await prisma.memory.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getMemoryWithPhotos(userId: string, memoryId: string): Promise<any> {
    const memory = await prisma.memory.findFirst({
      where: { id: memoryId, userId },
      include: {
        cluster: {
          include: {
            photos: {
              orderBy: { takenAt: 'asc' },
            },
          },
        },
        user: {
          select: { id: true, name: true },
        },
      },
    });

    if (!memory) return null;

    return {
      ...memory,
      photos: memory.cluster.photos,
    };
  }
}
