"use client"

import React, { useEffect, useState } from "react"
import { SessionProvider } from "@/components/SessionProvider"
import { ToastProvider } from "@/hooks/useToast"
import { ToastContainer } from "@/components/Toast"
import { ErrorBoundary } from "@/components/ErrorBoundary"
import { useToast } from "@/hooks/useToast"

function AppContent({ children }: { children: React.ReactNode }) {
  const { toasts, removeToast } = useToast()

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Application Error:', error, errorInfo)
        // Could send to error tracking service here
      }}
    >
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        {children}
      </div>
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </ErrorBoundary>
  )
}

export function ClientProviders({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <ToastProvider>
        <AppContent>{children}</AppContent>
      </ToastProvider>
    </SessionProvider>
  )
}
