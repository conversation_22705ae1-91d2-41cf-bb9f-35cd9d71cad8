'use client';

import React, { useEffect, useState } from "react";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ConnectCard } from "@/components/ConnectCard";
import { UploadCard } from "@/components/UploadCard";
import { TopPhotos } from "@/components/TopPhotos";
import { MemoryGrid } from "@/components/MemoryGrid";
import { PhotoGrid } from "@/components/PhotoGrid";

interface Photo {
  id: string;
  url: string;
  thumbUrl: string;
  takenAt: string | null;
  quality: number | null;
  significance: number | null;
  cluster?: {
    kind: string;
    label: string | null;
  };
}

interface Memory {
  id: string;
  title: string;
  summary: string;
  startAt: string;
  endAt: string;
  clusterId: string;
  coverPhotoId: string;
  coverPhoto?: {
    url: string;
    thumbUrl: string;
    takenAt: string | null;
  };
  cluster?: {
    kind: string;
    label: string | null;
    centerLat: number | null;
    centerLng: number | null;
  };
  photos?: Photo[];
}

export default function Dashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [memories, setMemories] = useState<Memory[]>([]);
  const [loading, setLoading] = useState(true);
  const [analyzing, setAnalyzing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'photos' | 'memories'>('overview');
  const [hasGoogleAccount, setHasGoogleAccount] = useState(false);
  const [googlePhotosImported, setGooglePhotosImported] = useState(false);

  useEffect(() => {
    if (status === "loading") return;
    if (!session) {
      router.push("/auth/signin");
    }
  }, [session, status, router]);

  useEffect(() => {
    if (session) {
      loadData();
    }
  }, [session]);

  const checkGoogleAccount = async () => {
    try {
      const response = await fetch('/api/ingest/google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pageToken: null }),
      });

      if (response.ok) {
        setHasGoogleAccount(true);
        setGooglePhotosImported(true);
        return true;
      } else if (response.status === 400) {
        // Google account not connected
        setHasGoogleAccount(false);
        setGooglePhotosImported(false);
        return false;
      }
    } catch (error) {
      console.error('Error checking Google account:', error);
      setHasGoogleAccount(false);
      setGooglePhotosImported(false);
      return false;
    }
  };

  const loadData = async () => {
    try {
      await Promise.all([
        checkGoogleAccount(),
        loadPhotos(),
        loadMemories(),
      ]);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPhotos = async () => {
    try {
      const response = await fetch('/api/photos?limit=20');
      if (response.ok) {
        const data = await response.json();
        setPhotos(data.photos || []);
      }
    } catch (error) {
      console.error('Error loading photos:', error);
    }
  };

  const loadMemories = async () => {
    try {
      const response = await fetch('/api/memories');
      if (response.ok) {
        const data = await response.json();
        setMemories(data.memories || []);
      }
    } catch (error) {
      console.error('Error loading memories:', error);
    }
  };

  const handleAnalysisComplete = () => {
    loadData(); // Reload all data after analysis
  };

  const handleGooglePhotos = async () => {
    try {
      const response = await fetch('/api/ingest/google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pageToken: null }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Google Photos import:', result);
        await loadData(); // Reload data after import
      }
    } catch (error) {
      console.error('Error importing from Google Photos:', error);
    }
  };

  if (status === "loading" || !session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show onboarding if no photos and user hasn't connected Google or imported photos
  if (photos.length === 0 && !loading && (!hasGoogleAccount || !googlePhotosImported)) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Welcome to Heirloom</h1>
              <p className="text-gray-600">Get started by connecting your photo library</p>
            </div>
            <Button
              onClick={() => signOut({ callbackUrl: "/" })}
              variant="outline"
            >
              Sign Out
            </Button>
          </div>

          <div className="grid md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            <div className="space-y-6">
              <ConnectCard />
            </div>
            <div className="space-y-6">
              <UploadCard />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome back, {session.user?.name}!</p>
          </div>
          <Button
            onClick={() => signOut({ callbackUrl: "/" })}
            variant="outline"
          >
            Sign Out
          </Button>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'photos', label: 'Photos' },
              { id: 'memories', label: 'Memories' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-gray-900">Photos</h3>
                      <p className="text-3xl font-bold text-blue-600">
                        {loading ? '...' : photos.length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-gray-900">Memories</h3>
                      <p className="text-3xl font-bold text-green-600">
                        {loading ? '...' : memories.length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-gray-900">Top Photos</h3>
                      <p className="text-sm text-gray-600">Ready for analysis</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Top Photos Section */}
            <TopPhotos
              userId={session.user.id}
              onAnalysisComplete={handleAnalysisComplete}
            />

            {/* Recent Memories */}
            {memories.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Recent Memories</h2>
                <MemoryGrid
                  memories={memories.slice(0, 3)}
                  showPhotos={true}
                />
              </div>
            )}
          </div>
        )}

        {activeTab === 'photos' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">All Photos</h2>
              <div className="flex space-x-4">
                <Button onClick={handleGooglePhotos}>
                  Import from Google Photos
                </Button>
                <Button variant="outline">
                  Upload More Photos
                </Button>
              </div>
            </div>

            <PhotoGrid
              photos={photos}
              showFeedback={true}
              hasMore={true}
              onLoadMore={loadPhotos}
            />
          </div>
        )}

        {activeTab === 'memories' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Your Memories</h2>
              <Button
                onClick={handleAnalysisComplete}
                disabled={analyzing || photos.length === 0}
              >
                {analyzing ? 'Analyzing...' : 'Generate New Memories'}
              </Button>
            </div>

            <MemoryGrid
              memories={memories}
              showPhotos={true}
            />
          </div>
        )}
      </div>
    </div>
  );
}
