import { prisma } from './prisma';

export interface SignificanceScore {
  photoId: string;
  totalScore: number;
  breakdown: {
    quality: number;
    people: number;
    context: number;
    subjectVariety: number;
    social: number;
    novelty: number;
  };
}

export class ScoringService {
  async calculateSignificanceScores(userId: string): Promise<SignificanceScore[]> {
    // Get all photos for the user
    const photos = await prisma.photo.findMany({
      where: { userId },
      include: {
        cluster: true,
      },
    });

    if (photos.length === 0) return [];

    // Get user settings for feature flags
    const userSettings = await prisma.userSettings.findUnique({
      where: { userId },
    });

    const enableFaces = userSettings?.enableFaces ?? false;

    const scores: SignificanceScore[] = [];

    for (const photo of photos) {
      const score = await this.calculatePhotoScore(photo, photos, enableFaces);
      scores.push(score);
    }

    // Sort by total score descending
    return scores.sort((a, b) => b.totalScore - a.totalScore);
  }

  private async calculatePhotoScore(
    photo: any,
    allPhotos: any[],
    enableFaces: boolean
  ): Promise<SignificanceScore> {
    // Calculate individual components
    const quality = this.calculateQualityScore(photo);
    const people = this.calculatePeopleScore(photo, enableFaces);
    const context = await this.calculateContextScore(photo, allPhotos);
    const subjectVariety = await this.calculateSubjectVarietyScore(photo, allPhotos);
    const social = this.calculateSocialScore(photo);
    const novelty = this.calculateNoveltyScore(photo, allPhotos);

    // Weighted combination
    const totalScore = Math.round(
      0.25 * quality +
      0.20 * people +
      0.20 * context +
      0.15 * subjectVariety +
      0.10 * social +
      0.10 * novelty
    );

    return {
      photoId: photo.id,
      totalScore: Math.min(Math.max(totalScore, 0), 100),
      breakdown: {
        quality,
        people,
        context,
        subjectVariety,
        social,
        novelty,
      },
    };
  }

  private calculateQualityScore(photo: any): number {
    // Normalize quality metrics to 0-100 scale
    let score = 0;

    if (photo.quality !== null) {
      score = Math.min(photo.quality, 100);
    } else {
      // Fallback: estimate from resolution
      const megapixels = photo.width && photo.height
        ? (photo.width * photo.height) / 1000000
        : 0;

      if (megapixels > 0) {
        score = Math.min(megapixels * 2, 100); // Rough heuristic
      }
    }

    return Math.round(score);
  }

  private calculatePeopleScore(photo: any, enableFaces: boolean): number {
    if (!enableFaces) return 0;

    if (photo.faces !== null) {
      // Bonus for multiple faces, cap at 100
      return Math.min(photo.faces >= 3 ? 100 : photo.faces * 30, 100);
    }

    return 0;
  }

  private async calculateContextScore(photo: any, allPhotos: any[]): Promise<number> {
    if (!photo.cluster || !photo.takenAt) return 50;

    // Get photos in the same cluster
    const clusterPhotos = allPhotos.filter(p => p.clusterId === photo.clusterId);

    if (clusterPhotos.length <= 1) return 50;

    // Calculate temporal proximity to cluster center
    const clusterStart = new Date(photo.cluster.startAt).getTime();
    const clusterEnd = new Date(photo.cluster.endAt).getTime();
    const clusterCenter = clusterStart + (clusterEnd - clusterStart) / 2;
    const photoTime = new Date(photo.takenAt).getTime();

    const temporalDistance = Math.abs(photoTime - clusterCenter);
    const clusterDuration = clusterEnd - clusterStart;

    // Normalize temporal proximity (closer to center = higher score)
    const temporalProximity = clusterDuration > 0
      ? 1 - (temporalDistance / clusterDuration)
      : 1;

    // Recency boost (newer photos get slight bonus)
    const now = Date.now();
    const daysOld = (now - photoTime) / (1000 * 60 * 60 * 24);
    const recencyBoost = Math.max(0, 1 - (daysOld / 365)); // 1 year decay

    const score = (temporalProximity * 70) + (recencyBoost * 30);
    return Math.round(Math.min(Math.max(score, 0), 100));
  }

  private async calculateSubjectVarietyScore(photo: any, allPhotos: any[]): Promise<number> {
    if (!photo.phash) return 50;

    // Get photos taken within 5 minutes
    const photoTime = new Date(photo.takenAt).getTime();
    const nearbyPhotos = allPhotos.filter(p => {
      if (!p.takenAt || !p.phash || p.id === photo.id) return false;
      const timeDiff = Math.abs(new Date(p.takenAt).getTime() - photoTime);
      return timeDiff <= 5 * 60 * 1000; // 5 minutes
    });

    if (nearbyPhotos.length === 0) return 100; // Unique moment

    // Calculate Hamming distance for perceptual similarity
    let maxSimilarity = 0;
    for (const nearby of nearbyPhotos) {
      const similarity = this.calculateSimilarity(photo.phash, nearby.phash);
      maxSimilarity = Math.max(maxSimilarity, similarity);
    }

    // Convert similarity to variety score (lower similarity = higher variety)
    const varietyScore = 100 - maxSimilarity;
    return Math.round(Math.max(0, varietyScore));
  }

  private calculateSocialScore(photo: any): number {
    let score = 0;

    // Check for favorites/stars (Google Photos)
    if (photo.source === 'google' && photo.exif) {
      try {
        const exifData = typeof photo.exif === 'string' ? JSON.parse(photo.exif) : photo.exif;
        if (exifData.favorite) score += 100;
      } catch (e) {
        // Ignore JSON parse errors
      }
    }

    // Cap at 100
    return Math.min(score, 100);
  }

  private calculateNoveltyScore(photo: any, allPhotos: any[]): number {
    if (!photo.takenAt) return 50;

    // Count photos taken in the same minute (burst detection)
    const photoTime = new Date(photo.takenAt).getTime();
    const sameMinutePhotos = allPhotos.filter(p => {
      if (!p.takenAt || p.id === photo.id) return false;
      const timeDiff = Math.abs(new Date(p.takenAt).getTime() - photoTime);
      return timeDiff <= 60 * 1000; // 1 minute
    });

    // Higher density = lower novelty
    const density = sameMinutePhotos.length;
    const noveltyScore = Math.max(0, 100 - (density * 20)); // -20 points per additional photo

    return Math.round(noveltyScore);
  }

  private calculateSimilarity(hash1: string, hash2: string): number {
    // Calculate Hamming distance for 64-bit hash
    let distance = 0;
    for (let i = 0; i < Math.min(hash1.length, hash2.length); i++) {
      if (hash1[i] !== hash2[i]) distance++;
    }

    // Convert to similarity percentage
    const maxLength = Math.max(hash1.length, hash2.length);
    return Math.round(((maxLength - distance) / maxLength) * 100);
  }

  async selectTopPhotos(userId: string, count: number = 10): Promise<SignificanceScore[]> {
    const allScores = await this.calculateSignificanceScores(userId);
    const topScores = allScores.slice(0, count);

    // Update significance scores in database
    for (const score of allScores) {
      await prisma.photo.update({
        where: { id: score.photoId },
        data: { significance: score.totalScore },
      });
    }

    return topScores;
  }

  async getDiverseTopPhotos(userId: string, count: number = 10): Promise<SignificanceScore[]> {
    const allScores = await this.calculateSignificanceScores(userId);

    if (allScores.length <= count) return allScores;

    // Apply diversity constraints
    const selected: SignificanceScore[] = [];
    const usedClusters = new Set<string>();

    // First pass: take highest scoring photos with cluster diversity
    for (const score of allScores) {
      const photo = await prisma.photo.findUnique({
        where: { id: score.photoId },
        select: { clusterId: true },
      });

      if (!photo?.clusterId || !usedClusters.has(photo.clusterId) || usedClusters.size >= 3) {
        selected.push(score);
        if (photo?.clusterId) usedClusters.add(photo.clusterId);
      }

      if (selected.length >= count) break;
    }

    // Second pass: fill remaining slots if needed
    if (selected.length < count) {
      for (const score of allScores) {
        if (!selected.find(s => s.photoId === score.photoId)) {
          selected.push(score);
          if (selected.length >= count) break;
        }
      }
    }

    return selected;
  }
}
