import { google, Auth } from 'googleapis';
import { prisma } from './prisma';

export interface GooglePhoto {
  id: string;
  baseUrl: string;
  mimeType: string;
  filename: string;
  creationTime: string;
  width?: number;
  height?: number;
  lat?: number;
  lng?: number;
  favorite?: boolean;
}

export interface GooglePhotosResponse {
  mediaItems: GooglePhoto[];
  nextPageToken?: string;
}

import { Account } from '@prisma/client';

export class ReauthenticationRequiredError extends Error {
  constructor() {
    super('Google access revoked. Please re-authenticate.');
    this.name = 'ReauthenticationRequiredError';
  }
}

export class GooglePhotosService {
  private oauth2Client: Auth.OAuth2Client;
  private account: Account;

  constructor(account: Account) {
    this.account = account;
    this.oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXTAUTH_URL}/api/auth/callback/google`
    );

    this.oauth2Client.setCredentials({
      access_token: account.access_token,
      refresh_token: account.refresh_token || undefined,
    });
  }
  
  private accountId?: string;

  async getPhotos(pageToken?: string, pageSize: number = 100): Promise<GooglePhotosResponse> {
    try {
      // Try to make API request directly
      const response = await this.oauth2Client.request<{ mediaItems: any[]; nextPageToken?: string }>({
        url: 'https://photoslibrary.googleapis.com/v1/mediaItems:search',
        method: 'POST',
        data: {
          pageSize,
          pageToken,
          filters: {
            mediaTypeFilter: {
              mediaTypes: ['PHOTO'],
            },
          },
        },
      });
      
      return this.parsePhotosResponse(response.data);
    } catch (error: any) {
      // If we get an authentication error, try to refresh token
      if (error.response?.data?.error === 'invalid_grant' || error.response?.status === 401) {
        try {
          // Force token refresh
          const newCredentials = await this.oauth2Client.refreshAccessToken();
          this.oauth2Client.setCredentials(newCredentials.credentials);
          
          // Update tokens in database
          await prisma.account.update({
            where: { id: this.account.id },
            data: {
              access_token: newCredentials.credentials.access_token,
              refresh_token: newCredentials.credentials.refresh_token || this.account.refresh_token,
              expires_at: newCredentials.credentials.expiry_date ? Math.floor(newCredentials.credentials.expiry_date / 1000) : undefined
            }
          });
          
          // Retry API request with new token
          const retryResponse = await this.oauth2Client.request<{ mediaItems: any[]; nextPageToken?: string }>({
            url: 'https://photoslibrary.googleapis.com/v1/mediaItems:search',
            method: 'POST',
            data: {
              pageSize,
              pageToken,
              filters: {
                mediaTypeFilter: {
                  mediaTypes: ['PHOTO'],
                },
              },
            },
          });
          
          return this.parsePhotosResponse(retryResponse.data);
        } catch (refreshError: any) {
          console.error('Failed to refresh access token:', refreshError);
          
          // Check if it's an invalid_grant error
          if (refreshError.response?.data?.error === 'invalid_grant') {
            // Handle token revocation
            await this.handleTokenRevocation();
            throw new ReauthenticationRequiredError();
          }
          
          throw new Error('Failed to refresh access token. Please try again later.');
        }
      }
      
      // Handle other errors
      if (error.response?.data?.error?.code === 403) {
        console.error('Google Photos API permission denied:', error.response.data.error.message);
        throw new Error(`Google Photos access denied: ${error.response.data.error.message}`);
      }
      
      console.error('Error fetching photos from Google Photos:', error);
      throw new Error('Failed to fetch photos from Google Photos. See logs for details.');
    }
  }

  private async handleTokenRevocation() {
    // Mark account as needing re-auth and clear tokens
    await prisma.account.update({
      where: { id: this.account.id },
      data: { 
        access_token: null,
        refresh_token: null,
        requires_reauth: true 
      }
    });
  }
  
  private parsePhotosResponse(data: any): GooglePhotosResponse {
    const mediaItems = data.mediaItems || [];
    return {
      mediaItems: mediaItems.map((item: any) => ({
        id: item.id,
        baseUrl: item.baseUrl,
        mimeType: item.mimeType,
        filename: item.filename,
        creationTime: item.mediaMetadata?.creationTime,
        width: item.mediaMetadata?.width ? parseInt(item.mediaMetadata.width) : undefined,
        height: item.mediaMetadata?.height ? parseInt(item.mediaMetadata.height) : undefined,
        lat: item.mediaMetadata?.location?.latitude ? parseFloat(item.mediaMetadata.location.latitude) : undefined,
        lng: item.mediaMetadata?.location?.longitude ? parseFloat(item.mediaMetadata.location.longitude) : undefined,
        favorite: item.favorite || false,
      })),
      nextPageToken: data.nextPageToken,
    };
  }

  async savePhotosToDatabase(userId: string, photos: GooglePhoto[]) {
    const photoData = photos.map(photo => ({
      userId,
      source: 'google' as const,
      sourceId: photo.id,
      url: `${photo.baseUrl}=w2048-h1536`,
      thumbUrl: `${photo.baseUrl}=w400-h300`,
      takenAt: new Date(photo.creationTime),
      lat: photo.lat,
      lng: photo.lng,
      width: photo.width,
      height: photo.height,
      exif: JSON.stringify({
        mimeType: photo.mimeType,
        filename: photo.filename,
      }),
    }));

    // Use createMany for better performance
    await prisma.photo.createMany({
      data: photoData,
      skipDuplicates: true,
    } as any);

    return photoData.length;
  }
}
