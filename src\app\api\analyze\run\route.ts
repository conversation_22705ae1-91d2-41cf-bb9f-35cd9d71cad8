import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ClusteringService } from '@/lib/clustering';
import { ScoringService } from '@/lib/scoring';
import { MemoryGenerationService } from '@/lib/memory-generation';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any;

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { regenerate } = await request.json();

    if (regenerate) {
      // Clear existing clusters and memories
      await prisma.memory.deleteMany({
        where: { userId: session.user.id },
      });

      await prisma.cluster.deleteMany({
        where: { userId: session.user.id },
      });

      // Reset photo cluster assignments
      await prisma.photo.updateMany({
        where: { userId: session.user.id },
        data: { clusterId: null },
      });
    }

    // Step 1: Cluster photos
    console.log('Starting photo clustering...');
    const clusteringService = new ClusteringService();
    const clusters = await clusteringService.clusterPhotos(session.user.id);
    console.log(`Created ${clusters.length} clusters`);

    // Step 2: Calculate significance scores
    console.log('Calculating significance scores...');
    const scoringService = new ScoringService();
    const topPhotos = await scoringService.getDiverseTopPhotos(session.user.id, 10);
    console.log(`Selected ${topPhotos.length} top photos`);

    // Step 3: Generate memories
    console.log('Generating memories...');
    const memoryService = new MemoryGenerationService();
    const memories = await memoryService.generateMemories(session.user.id);
    console.log(`Generated ${memories.length} memories`);

    // Step 4: Return results
    return NextResponse.json({
      success: true,
      stats: {
        clustersCreated: clusters.length,
        topPhotosSelected: topPhotos.length,
        memoriesGenerated: memories.length,
      },
      topPhotos: topPhotos.map(score => ({
        photoId: score.photoId,
        score: score.totalScore,
        breakdown: score.breakdown,
      })),
      memories: memories.map(memory => ({
        id: memory.id,
        title: memory.title,
        clusterId: memory.clusterId,
        startAt: memory.startAt,
        endAt: memory.endAt,
      })),
    });

  } catch (error) {
    console.error('Error in analysis run:', error);
    return NextResponse.json(
      { error: 'Failed to run analysis' },
      { status: 500 }
    );
  }
}
