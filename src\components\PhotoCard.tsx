"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"

interface Photo {
  id: string
  url: string
  thumbUrl: string
  takenAt: string | null
  quality: number | null
  significance: number | null
  cluster?: {
    kind: string
    label: string | null
  }
}

interface SignificanceBreakdown {
  quality: number
  people: number
  context: number
  subjectVariety: number
  social: number
  novelty: number
}

interface PhotoCardProps {
  photo: Photo
  scoreBreakdown?: SignificanceBreakdown
  onFeedback?: (photoId: string, type: 'upvote' | 'downvote') => void
  showFeedback?: boolean
  size?: 'small' | 'medium' | 'large'
}

export function PhotoCard({
  photo,
  scoreBreakdown,
  onFeedback,
  showFeedback = false,
  size = 'medium'
}: PhotoCardProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  const sizeClasses = {
    small: 'aspect-square w-24 h-24',
    medium: 'aspect-square w-32 h-32',
    large: 'aspect-square w-48 h-48'
  }

  const handleImageError = () => {
    setImageError(true)
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString()
  }

  const getScoreColor = (score: number | null) => {
    if (!score) return 'text-gray-400'
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreLabel = (score: number | null) => {
    if (!score) return 'Unscored'
    if (score >= 80) return 'Excellent'
    if (score >= 60) return 'Good'
    return 'Needs Improvement'
  }

  return (
    <Card className={`${sizeClasses[size]} group relative overflow-hidden hover:shadow-lg transition-all duration-300 ${!imageLoaded ? 'animate-pulse bg-gray-200' : ''}`}>
      <CardContent className="p-0 h-full">
        {!imageError ? (
          <img
            src={photo.thumbUrl || photo.url}
            alt=""
            className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
              !imageLoaded ? 'opacity-0' : 'opacity-100'
            }`}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
          </div>
        )}

        {/* Overlay with metadata */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="absolute bottom-0 left-0 right-0 p-2 text-white">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs font-medium">
                {photo.cluster?.kind === 'trip' ? '🏕️ Trip' : '🏠 Home'}
              </span>
              {photo.significance && (
                <span className={`text-xs font-semibold ${getScoreColor(photo.significance)}`}>
                  {Math.round(photo.significance)}
                </span>
              )}
            </div>

            {photo.takenAt && (
              <p className="text-xs opacity-90 truncate">
                {formatDate(photo.takenAt)}
              </p>
            )}

            {photo.cluster?.label && (
              <p className="text-xs opacity-75 truncate">
                {photo.cluster.label}
              </p>
            )}
          </div>
        </div>

        {/* Score tooltip */}
        {scoreBreakdown && (
          <div className="absolute top-2 right-2 bg-black/80 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-10">
            <div className="font-semibold mb-1">
              Score: {Math.round(
                0.25 * scoreBreakdown.quality +
                0.20 * scoreBreakdown.people +
                0.20 * scoreBreakdown.context +
                0.15 * scoreBreakdown.subjectVariety +
                0.10 * scoreBreakdown.social +
                0.10 * scoreBreakdown.novelty
              )}
            </div>
            <div className="space-y-1">
              <div>Quality: {scoreBreakdown.quality.toFixed(1)}</div>
              <div>People: {scoreBreakdown.people.toFixed(1)}</div>
              <div>Context: {scoreBreakdown.context.toFixed(1)}</div>
              <div>Variety: {scoreBreakdown.subjectVariety.toFixed(1)}</div>
              <div>Social: {scoreBreakdown.social.toFixed(1)}</div>
              <div>Novelty: {scoreBreakdown.novelty.toFixed(1)}</div>
            </div>
          </div>
        )}

        {/* Feedback buttons */}
        {showFeedback && onFeedback && (
          <div className="absolute top-2 left-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button
              onClick={() => onFeedback(photo.id, 'upvote')}
              className="bg-green-500 hover:bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold transition-colors"
              title="Good photo!"
            >
              ↑
            </button>
            <button
              onClick={() => onFeedback(photo.id, 'downvote')}
              className="bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold transition-colors"
              title="Not great"
            >
              ↓
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
