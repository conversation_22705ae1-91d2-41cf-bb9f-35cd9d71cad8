"use client"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/useToast"

interface UploadResult {
  id?: string;
  filename: string;
  success: boolean;
  error?: string;
  metadata?: {
    width: number;
    height: number;
    quality: number;
    sharpness: number;
    exposureBalance: number;
    takenAt: string | null;
    hasLocation: boolean;
  };
}

export function UploadCard() {
  const [isDragOver, setIsDragOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([])
  const [showResults, setShowResults] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { addToast } = useToast()

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    handleFiles(files)
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    handleFiles(files)
  }

  const handleFiles = async (files: File[]) => {
    const imageFiles = files.filter(file => {
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif', 'image/webp']
      return validTypes.includes(file.type.toLowerCase()) && file.size <= 20 * 1024 * 1024
    })

    if (imageFiles.length === 0) {
      addToast('error', 'Invalid Files', 'Please select valid image files (JPG, PNG, HEIC, WebP) under 20MB each')
      return
    }

    if (imageFiles.length > 50) {
      addToast('warning', 'Too Many Files', 'Please select no more than 50 photos at a time')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)
    setShowResults(false)
    setUploadResults([])

    try {
      const formData = new FormData()
      imageFiles.forEach(file => {
        formData.append('files', file)
      })

      const response = await fetch('/api/ingest/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const result = await response.json()
      setUploadResults(result.results || [])
      setShowResults(true)

      if (result.successCount > 0) {
        addToast('success', 'Upload Complete', `Successfully uploaded ${result.successCount} photos!`)
        // Trigger analysis if we have successful uploads
        setTimeout(() => {
          if (confirm(`Successfully uploaded ${result.successCount} photos! Would you like to analyze them now?`)) {
            window.location.href = '/dashboard'
          }
        }, 1000)
      }

      if (result.failureCount > 0) {
        addToast('warning', 'Some Files Failed', `${result.failureCount} files couldn't be uploaded`)
      }

    } catch (error) {
      console.error('Upload error:', error)
      addToast('error', 'Upload Failed', error instanceof Error ? error.message : 'Unknown error occurred')
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <Card className="h-full group hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 bg-white/90 backdrop-blur-sm border-0 shadow-xl relative overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 via-transparent to-pink-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      <CardHeader className="text-center pb-8 relative z-10">
        <div className="relative">
          <div className="w-24 h-24 bg-gradient-to-br from-purple-500 via-purple-600 to-pink-600 rounded-3xl flex items-center justify-center mb-8 mx-auto shadow-xl group-hover:scale-110 group-hover:-rotate-3 transition-all duration-500">
            <svg className="w-12 h-12 text-white group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          {/* Floating accent elements */}
          <div className="absolute -top-2 -left-2 w-5 h-5 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full animate-ping delay-100"></div>
          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full animate-pulse delay-500"></div>
        </div>
        <CardTitle className="text-3xl font-bold text-gray-900 mb-4 group-hover:text-purple-900 transition-colors duration-300">
          Upload from Device
        </CardTitle>
        <CardDescription className="text-lg text-gray-600 leading-relaxed">
          Drag and drop photos or click to browse your files and let AI analyze your memories
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center space-y-8 px-8 pb-10 relative z-10">
        <div
          className={`w-full h-40 border-3 border-dashed rounded-2xl flex items-center justify-center cursor-pointer transition-all duration-500 relative overflow-hidden ${
            isDragOver
              ? 'border-purple-500 bg-gradient-to-br from-purple-50 via-purple-100 to-pink-100 shadow-inner scale-[1.02]'
              : 'border-gray-300 hover:border-purple-400 hover:bg-gray-50'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          {/* Animated background effect */}
          <div className={`absolute inset-0 bg-gradient-to-r from-purple-400/10 via-pink-400/10 to-purple-400/10 transition-opacity duration-300 ${isDragOver ? 'opacity-100' : 'opacity-0'}`}></div>

          {isUploading ? (
            <div className="text-center relative z-10">
              <div className="relative">
                <div className="animate-spin w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full mx-auto mb-4"></div>
                <div className="absolute inset-0 w-12 h-12 border-4 border-purple-300 border-t-transparent rounded-full mx-auto animate-ping"></div>
              </div>
              <p className="text-lg font-semibold text-purple-700 animate-pulse">Analyzing photos with AI...</p>
              <p className="text-sm text-purple-600 mt-2">Extracting EXIF data and generating image features</p>
              <div className="mt-4 w-48 h-2 bg-purple-100 rounded-full mx-auto overflow-hidden">
                <div className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse" style={{ width: `${uploadProgress}%` }}></div>
              </div>
            </div>
          ) : showResults ? (
            <div className="text-center relative z-10">
              <div className="mb-4">
                {uploadResults.filter(r => r.success).length > 0 && (
                  <div className="text-green-600 mb-2">
                    <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <p className="font-semibold">
                      {uploadResults.filter(r => r.success).length} photos uploaded successfully!
                    </p>
                  </div>
                )}
                {uploadResults.filter(r => !r.success).length > 0 && (
                  <div className="text-red-600 mb-2">
                    <p className="font-semibold">
                      {uploadResults.filter(r => !r.success).length} photos failed to upload
                    </p>
                  </div>
                )}
              </div>
              <Button
                onClick={() => setShowResults(false)}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Upload More Photos
              </Button>
            </div>
          ) : (
            <div className="text-center relative z-10">
              <svg className={`w-12 h-12 mx-auto mb-4 transition-all duration-300 ${
                isDragOver ? 'text-purple-600 scale-110' : 'text-gray-400'
              }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <p className={`text-lg font-semibold transition-colors duration-300 ${
                isDragOver ? 'text-purple-700' : 'text-gray-600'
              }`}>
                {isDragOver ? '✨ Drop photos here' : 'Click to browse or drag photos here'}
              </p>
              <p className={`text-sm transition-colors duration-300 ${
                isDragOver ? 'text-purple-600' : 'text-gray-500'
              }`}>
                {isDragOver ? 'Release to start AI analysis' : 'Supports JPG, PNG, HEIC, WebP • Max 20MB each • Up to 50 photos'}
              </p>
            </div>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        <Button
          variant="outline"
          onClick={handleClick}
          disabled={isUploading}
          className="w-full border-2 border-purple-200 text-purple-700 hover:bg-purple-50 hover:border-purple-300 font-semibold py-5 px-8 rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-lg relative overflow-hidden group/button"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/0 via-purple-500/5 to-purple-500/0 translate-x-[-100%] group-hover/button:translate-x-[100%] transition-transform duration-700"></div>
          {isUploading ? (
            <>
              <div className="animate-spin w-5 h-5 border-2 border-purple-600 border-t-transparent rounded-full mr-3 relative z-10"></div>
              <span className="relative z-10 text-lg">Processing with AI...</span>
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-3 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span className="relative z-10 text-lg">Choose Photos</span>
            </>
          )}
        </Button>

        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 w-full border border-purple-100">
          <p className="text-sm text-purple-700 text-center font-medium leading-relaxed">
            📸 <strong>Smart Upload:</strong> Supports JPG, PNG, HEIC, and other image formats • AI-powered analysis included
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
