"use client"

import { Card, CardContent } from "@/components/ui/card"

interface LoadingProps {
  size?: 'small' | 'medium' | 'large'
  message?: string
  overlay?: boolean
}

export const Loading = ({ size = 'medium', message = 'Loading...', overlay = false }: LoadingProps) => {
  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-8 h-8',
    large: 'w-12 h-12'
  }

  const spinner = (
    <div className="flex flex-col items-center justify-center space-y-3">
      <div className="relative">
        <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]}`}></div>
        {size === 'large' && (
          <div className={`absolute inset-0 animate-ping rounded-full border-2 border-blue-600 opacity-20 ${sizeClasses[size]}`}></div>
        )}
      </div>
      {message && (
        <p className="text-sm text-gray-600 animate-pulse">{message}</p>
      )}
    </div>
  )

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
        {spinner}
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center p-8">
      {spinner}
    </div>
  )
}

export const LoadingCard = ({ message = 'Loading...' }: { message?: string }) => {
  return (
    <Card>
      <CardContent className="p-8">
        <Loading message={message} />
      </CardContent>
    </Card>
  )
}

export const LoadingGrid = ({ count = 6 }: { count?: number }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse">
          <CardContent className="p-6">
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              <div className="h-16 bg-gray-200 rounded"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
