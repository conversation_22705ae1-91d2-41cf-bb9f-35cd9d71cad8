import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ImageAnalysisService } from '@/lib/image-analysis';
import { prisma } from '@/lib/prisma';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any;

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'No files uploaded' }, { status: 400 });
    }

    // Validate file types
    const validFiles = files.filter(file => {
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif', 'image/webp'];
      return validTypes.includes(file.type.toLowerCase()) && file.size <= 20 * 1024 * 1024; // 20MB limit
    });

    if (validFiles.length === 0) {
      return NextResponse.json({
        error: 'No valid image files found. Please upload JPG, PNG, HEIC, or WebP files under 20MB each.'
      }, { status: 400 });
    }

    const imageAnalysis = new ImageAnalysisService();
    const results = [];

    for (const file of validFiles) {
      try {
        // Convert file to ArrayBuffer for processing
        const arrayBuffer = await file.arrayBuffer();

        // Extract metadata and analyze image
        const metadata = await imageAnalysis.extractMetadata(arrayBuffer);

        // Calculate perceptual hash
        const phash = await imageAnalysis.calculatePHash(arrayBuffer);

        // For now, store as base64 data URL (in production, upload to cloud storage)
        const base64Data = Buffer.from(arrayBuffer).toString('base64');
        const mimeType = file.type || 'image/jpeg';
        const dataUrl = `data:${mimeType};base64,${base64Data}`;

        // Create thumbnail (simple resize for now)
        const thumbBuffer = await sharp(arrayBuffer)
          .resize(400, 400, { fit: 'inside', withoutEnlargement: true })
          .jpeg({ quality: 80 })
          .toBuffer();
        const thumbBase64 = thumbBuffer.toString('base64');
        const thumbDataUrl = `data:image/jpeg;base64,${thumbBase64}`;

        // Save photo to database
        const photo = await prisma.photo.create({
          data: {
            userId: session.user.id,
            source: 'upload',
            sourceId: `${Date.now()}-${file.name}`,
            url: dataUrl,
            thumbUrl: thumbDataUrl,
            takenAt: metadata.takenAt,
            lat: metadata.lat,
            lng: metadata.lng,
            geohash: metadata.geohash,
            width: metadata.width,
            height: metadata.height,
            exif: metadata.exif,
            quality: metadata.quality,
            phash: phash,
            significance: 0, // Will be calculated later
          },
        });

        results.push({
          id: photo.id,
          filename: file.name,
          success: true,
          metadata: {
            width: metadata.width,
            height: metadata.height,
            quality: metadata.quality,
            sharpness: metadata.sharpness,
            exposureBalance: metadata.exposureBalance,
            takenAt: metadata.takenAt,
            hasLocation: !!(metadata.lat && metadata.lng),
          },
        });

      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
        results.push({
          filename: file.name,
          success: false,
          error: `Failed to process image: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return NextResponse.json({
      message: `Successfully processed ${successCount} of ${validFiles.length} photos${failureCount > 0 ? `, ${failureCount} failed` : ''}`,
      successCount,
      failureCount,
      results,
    });

  } catch (error) {
    console.error('Error in photo upload:', error);
    return NextResponse.json(
      { error: 'Failed to process uploaded photos' },
      { status: 500 }
    );
  }
}
