import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface FeedbackRequest {
  photoId: string;
  type: 'upvote' | 'downvote';
  reason?: string;
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any;

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { photoId, type, reason }: FeedbackRequest = await request.json();

    if (!photoId || !type) {
      return NextResponse.json({
        error: 'Missing required fields: photoId and type'
      }, { status: 400 });
    }

    if (!['upvote', 'downvote'].includes(type)) {
      return NextResponse.json({
        error: 'Type must be either "upvote" or "downvote"'
      }, { status: 400 });
    }

    // Verify the photo belongs to the user
    const photo = await prisma.photo.findFirst({
      where: {
        id: photoId,
        userId: session.user.id,
      },
    });

    if (!photo) {
      return NextResponse.json({
        error: 'Photo not found or access denied'
      }, { status: 404 });
    }

    // Update photo significance score based on feedback
    let scoreAdjustment = 0;
    if (type === 'upvote') {
      scoreAdjustment = 10; // Boost score for positive feedback
    } else if (type === 'downvote') {
      scoreAdjustment = -10; // Reduce score for negative feedback
    }

    const newSignificance = Math.max(0, Math.min(100,
      (photo.significance || 50) + scoreAdjustment
    ));

    await prisma.photo.update({
      where: { id: photoId },
      data: { significance: newSignificance },
    });

    // Store feedback record for analytics
    await prisma.feedback.create({
      data: {
        userId: session.user.id,
        photoId,
        type,
        reason: reason || null,
        scoreBefore: photo.significance || 50,
        scoreAfter: newSignificance,
      },
    });

    return NextResponse.json({
      success: true,
      message: `Photo ${type}d successfully`,
      newSignificance,
    });

  } catch (error) {
    console.error('Error processing feedback:', error);
    return NextResponse.json(
      { error: 'Failed to process feedback' },
      { status: 500 }
    );
  }
}
