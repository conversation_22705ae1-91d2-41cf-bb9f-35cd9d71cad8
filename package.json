{"name": "heirloom", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@types/sharp": "^0.31.1", "bullmq": "^5.58.0", "exifr": "^7.1.3", "geohash": "^0.0.1", "googleapis": "^156.0.0", "haversine": "^1.1.1", "ioredis": "^5.7.0", "next": "15.5.0", "next-auth": "^4.24.11", "ngeohash": "^0.6.3", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "^0.34.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@prisma/client": "^6.14.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.5.0", "postcss": "^8.5.6", "prisma": "^6.14.0", "tailwindcss": "^3.4.17", "typescript": "^5"}}