import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { ConnectCard } from "@/components/ConnectCard"
import { UploadCard } from "@/components/UploadCard"

export default async function HomePage() {
  const session = await getServerSession(authOptions)

  if (session) {
    // User is logged in, redirect to dashboard or show their content
    return (
      <main className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/20 to-pink-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="container mx-auto px-4 py-12 relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 rounded-2xl mb-8 shadow-2xl animate-float">
              <svg className="w-12 h-12 text-white animate-glow" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 12l-6-4h12l-6 4z" />
                <path d="M10 8l6-4H4l6 4z" />
              </svg>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6 animate-fade-in">
              Welcome back, {session.user?.name}!
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-slide-up">
              Ready to transform your photos into meaningful memories and beautiful stories with the power of AI?
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <div className="animate-slide-up delay-200">
              <ConnectCard />
            </div>
            <div className="animate-slide-up delay-400">
              <UploadCard />
            </div>
          </div>

          {/* Additional feature highlights */}
          <div className="mt-24 text-center">
            <div className="inline-block p-1 bg-gradient-to-r from-blue-500 via-purple-600 to-pink-600 rounded-2xl">
              <div className="bg-white/90 backdrop-blur-sm rounded-xl px-8 py-6 shadow-lg">
                <h2 className="text-2xl md:text-3xl font-bold gradient-text mb-4">
                  Your Photos, Your Stories
                </h2>
                <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                  Discover the moments that matter most with AI-powered photo analysis and automatic story generation.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    )
  }

  // User is not logged in, show login options
  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-blue-400/15 to-purple-600/15 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-indigo-400/15 to-pink-600/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-cyan-400/10 to-blue-600/10 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto px-4 py-16 relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center justify-center w-28 h-28 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 rounded-3xl mb-10 shadow-2xl animate-float">
            <svg className="w-14 h-14 text-white animate-glow" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 12l-6-4h12l-6 4z" />
              <path d="M10 8l6-4H4l6 4z" />
            </svg>
          </div>
          <h1 className="text-6xl md:text-8xl font-bold gradient-text mb-8 text-balance animate-fade-in">
            Heirloom
          </h1>
          <p className="text-2xl md:text-3xl text-gray-600 max-w-4xl mx-auto mb-6 text-balance leading-relaxed animate-slide-up">
            Transform your photos into memories
          </p>
          <p className="text-xl text-gray-500 max-w-3xl mx-auto text-balance leading-relaxed animate-slide-up delay-200">
            Discover your most significant moments and create beautiful stories from your photo library using AI-powered analysis
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto">
          <div className="animate-slide-up delay-400">
            <ConnectCard />
          </div>
          <div className="animate-slide-up delay-600">
            <UploadCard />
          </div>
        </div>

        <div className="mt-20 text-center">
          <h2 className="text-3xl font-bold gradient-text mb-12 text-balance">
            Why Choose Heirloom?
          </h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="glass-card rounded-xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 group hover:scale-[1.02]">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Smart Analysis</h3>
              <p className="text-gray-600 text-sm">AI-powered photo analysis to find your most meaningful moments</p>
            </div>
            <div className="glass-card rounded-xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 group hover:scale-[1.02]">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Memory Stories</h3>
              <p className="text-gray-600 text-sm">Automatically generated stories and albums from your photo trips</p>
            </div>
            <div className="glass-card rounded-xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 group hover:scale-[1.02]">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Privacy First</h3>
              <p className="text-gray-600 text-sm">Your photos never leave your device in local-only mode</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
