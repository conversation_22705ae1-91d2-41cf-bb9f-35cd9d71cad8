# Gallery Ingest and Memory Generator

A modern, full-stack web application that ingests photo libraries, analyzes them with AI-powered algorithms, and automatically generates meaningful memories from your photo collections.

## ✨ Features

- **📸 Google Photos Integration** - Seamlessly import photos from Google Photos with metadata
- **📤 File Upload** - Drag-and-drop upload for local photo files
- **🧠 Smart Analysis** - Advanced image analysis including quality, sharpness, and perceptual hashing
- **📍 Trip Detection** - Automatically clusters photos into trips and home moments based on time and location
- **🎯 Significance Scoring** - AI-powered scoring system to identify the most important photos
- **💭 Memory Generation** - Automatically creates titled memories with contextual summaries
- **🎨 Modern UI** - Clean, responsive interface built with Next.js and TailwindCSS

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm
- Google OAuth2 credentials (for Google Photos integration)

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd gallery-memory-generator
   pnpm install
   ```

2. **Set up the database:**
   ```bash
   pnpm prisma migrate dev
   pnpm prisma generate
   ```

3. **Configure environment variables:**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` and add your configuration:
   ```env
   # Database
   DATABASE_URL="file:./dev.db"

   # NextAuth.js
   NEXTAUTH_SECRET="your-secret-key-here"
   NEXTAUTH_URL="http://localhost:3000"

   # Google OAuth
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"
   ```

4. **Start the development server:**
   ```bash
   pnpm dev
   ```

   Open [http://localhost:3001](http://localhost:3001) in your browser.

## 🔧 Configuration

### Google OAuth Setup

⚠️ **Important: Google Photos API Changes (March 31, 2025)**

Google has removed the `photoslibrary.readonly` scope that allowed apps to read all user photos. This app now uses:
- `photoslibrary.appendonly` - for uploading new photos
- `photoslibrary.readonly.appcreateddata` - for reading only photos uploaded by this app

**This means the app can only access photos that users upload through the app itself, not their existing Google Photos library.**

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Photos Library API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)
6. Copy Client ID and Client Secret to your `.env` file

### Alternative Approaches for Existing Photos

If you need to access existing user photos, consider:
1. **Google Photos Picker API**: Allows users to manually select photos through a picker interface
2. **Google Takeout**: Users can export their photos and upload them to your app
3. **Manual Upload**: Users upload photos directly to your application

## 📊 How It Works

### 1. Photo Ingestion
- **Google Photos**: Authenticated API access to fetch photos with metadata
- **File Upload**: Client-side processing with EXIF extraction and image analysis

### 2. Image Analysis
- **Quality Assessment**: Resolution, format, color space analysis
- **Sharpness Detection**: Laplacian variance calculation
- **Exposure Balance**: Histogram analysis for proper exposure
- **Perceptual Hashing**: dHash for duplicate detection

### 3. Clustering Algorithm
- **Time-based**: Groups photos taken within 6-hour windows
- **Distance-based**: Splits groups if photos are >100km apart
- **Classification**: Determines if cluster is a "trip" or "home" based on duration and distance

### 4. Significance Scoring
Multi-factor scoring (0-100 scale):
- **Quality (25%)**: Image technical quality
- **Context (20%)**: Temporal position within cluster
- **People (20%)**: Face detection (when enabled)
- **Subject Variety (15%)**: Uniqueness within cluster
- **Social (10%)**: Google Photos favorites/stars
- **Novelty (10%)**: Burst detection and density analysis

### 5. Memory Generation
- **Title Generation**: Contextual titles based on trip type and location
- **Summary Creation**: AI-generated descriptions of the captured moments
- **Cover Selection**: Highest-scoring photo from each cluster

## 🏗️ Architecture

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── dashboard/         # Main dashboard
│   └── auth/              # Authentication pages
├── lib/                   # Business logic
│   ├── auth.ts           # NextAuth configuration
│   ├── clustering.ts     # Photo clustering logic
│   ├── scoring.ts        # Significance scoring
│   ├── memory-generation.ts # Memory creation
│   ├── image-analysis.ts # Image processing
│   └── google-photos.ts  # Google Photos API
└── prisma/               # Database schema
    └── schema.prisma
```

## 🎨 UI Components

- **Dashboard**: Main interface with stats, action buttons, and photo grids
- **Photo Grid**: Responsive display of photos with thumbnails
- **Memory Cards**: Beautiful cards showing memory titles and summaries
- **Connect/Upload Cards**: Onboarding flow for photo sources

## 🔒 Privacy & Security

- **Local-first**: All analysis happens locally, no images uploaded to third parties
- **Privacy Controls**: Optional face detection and location processing
- **Data Ownership**: Users retain full control of their photos and metadata
- **Minimal Scopes**: Google Photos integration requests only necessary permissions

## 📈 Performance

- **Efficient Processing**: Batch operations for large photo libraries
- **Optimized Queries**: Database indexing for fast photo retrieval
- **Progressive Loading**: Pagination and lazy loading for large datasets
- **Background Jobs**: Non-blocking analysis with optional Redis queuing

## 🚀 Deployment

### Vercel (Recommended)

1. Push code to GitHub
2. Connect repository to Vercel
3. Add environment variables in Vercel dashboard
4. Set up Google OAuth redirect URIs with production domain
5. Deploy!

### Other Platforms

The app is compatible with any platform that supports Next.js:
- Netlify
- Railway
- AWS Amplify
- Self-hosted with Docker

## 🧪 Testing

```bash
# Run tests
pnpm test

# Run E2E tests
pnpm test:e2e

# Type checking
pnpm type-check

# Linting
pnpm lint
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Troubleshooting

### Common Issues

**Google Photos Import Fails**
- Verify OAuth credentials are correct
- Ensure Google Photos Library API is enabled
- Check redirect URIs match your domain

**Database Connection Issues**
- Run `pnpm prisma generate` after schema changes
- Check DATABASE_URL in environment variables
- Ensure SQLite file has proper permissions

**Image Analysis Errors**
- Verify Sharp is properly installed
- Check file permissions for image processing
- Ensure sufficient memory for large images

### Support

For issues and questions:
1. Check the troubleshooting guide above
2. Search existing GitHub issues
3. Create a new issue with detailed information

---

Built with ❤️ using Next.js, TypeScript, TailwindCSS, and Prisma
