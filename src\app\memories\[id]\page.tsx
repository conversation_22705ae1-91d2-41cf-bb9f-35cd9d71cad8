"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { PhotoGrid } from "@/components/PhotoGrid"

interface Memory {
  id: string
  title: string
  summary: string
  startAt: string
  endAt: string
  clusterId: string
  coverPhotoId: string
  coverPhoto?: {
    url: string
    thumbUrl: string
    takenAt: string | null
  }
  cluster?: {
    kind: string
    label: string | null
    centerLat: number | null
    centerLng: number | null
  }
}

interface Photo {
  id: string
  url: string
  thumbUrl: string
  takenAt: string | null
  quality: number | null
  significance: number | null
  cluster?: {
    kind: string
    label: string | null
  }
}

interface MemoryDetail {
  memory: Memory
  photos: Photo[]
}

export default function MemoryDetailPage() {
  const { id } = useParams()
  const router = useRouter()
  const [memoryDetail, setMemoryDetail] = useState<MemoryDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (id) {
      loadMemoryDetail(id as string)
    }
  }, [id])

  const loadMemoryDetail = async (memoryId: string) => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/memories/${memoryId}`)

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Memory not found')
        }
        throw new Error('Failed to load memory')
      }

      const data = await response.json()
      setMemoryDetail(data)
    } catch (err) {
      console.error('Error loading memory:', err)
      setError(err instanceof Error ? err.message : 'Failed to load memory')
    } finally {
      setLoading(false)
    }
  }

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return start.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      })
    }

    return `${start.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric'
    })} - ${end.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    })}`
  }

  const getLocationText = (memory: Memory) => {
    if (memory.cluster?.label) {
      return memory.cluster.label
    }
    if (memory.cluster?.centerLat && memory.cluster?.centerLng) {
      return `${memory.cluster.centerLat.toFixed(4)}, ${memory.cluster.centerLng.toFixed(4)}`
    }
    return 'Unknown Location'
  }

  const getMemoryIcon = (kind: string) => {
    return kind === 'trip' ? '🏕️' : '🏠'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="h-64 bg-gray-200 rounded-lg mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="h-32 bg-gray-200 rounded"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !memoryDetail) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Memory Not Found</h1>
          <p className="text-gray-600 mb-6">
            {error || "The memory you're looking for doesn't exist or has been removed."}
          </p>
          <Button onClick={() => router.push('/dashboard')}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    )
  }

  const { memory, photos } = memoryDetail

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => router.push('/dashboard')}
            className="mb-4"
          >
            ← Back to Dashboard
          </Button>

          <div className="flex items-start justify-between">
            <div>
              <div className="flex items-center mb-2">
                <span className="text-2xl mr-2">
                  {getMemoryIcon(memory.cluster?.kind || 'home')}
                </span>
                <h1 className="text-3xl font-bold text-gray-900">{memory.title}</h1>
              </div>
              <p className="text-lg text-gray-600 mb-2">
                {formatDateRange(memory.startAt, memory.endAt)}
              </p>
              <div className="flex items-center text-gray-500">
                <svg className="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <span>{getLocationText(memory)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Cover Photo */}
        {memory.coverPhoto && (
          <div className="mb-8">
            <Card className="overflow-hidden">
              <img
                src={memory.coverPhoto.url}
                alt={memory.title}
                className="w-full h-96 object-cover"
                onError={(e) => {
                  e.currentTarget.src = memory.coverPhoto?.thumbUrl || ''
                }}
              />
            </Card>
          </div>
        )}

        {/* Memory Details */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Story</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {memory.summary}
                </p>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold text-sm text-gray-600 mb-1">Type</h4>
                  <p className="text-gray-900">
                    {memory.cluster?.kind === 'trip' ? '🏕️ Trip' : '🏠 Home'}
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold text-sm text-gray-600 mb-1">Photos</h4>
                  <p className="text-gray-900">{photos.length} photos</p>
                </div>

                <div>
                  <h4 className="font-semibold text-sm text-gray-600 mb-1">Duration</h4>
                  <p className="text-gray-900">
                    {Math.ceil((new Date(memory.endAt).getTime() - new Date(memory.startAt).getTime()) / (1000 * 60 * 60 * 24))} days
                  </p>
                </div>

                {memory.cluster?.centerLat && memory.cluster?.centerLng && (
                  <div>
                    <h4 className="font-semibold text-sm text-gray-600 mb-1">Coordinates</h4>
                    <p className="text-gray-900 text-sm">
                      {memory.cluster.centerLat.toFixed(4)}, {memory.cluster.centerLng.toFixed(4)}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Photos */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Photos ({photos.length})
          </h2>

          <PhotoGrid
            photos={photos}
            showFeedback={true}
            emptyMessage="No photos found in this memory"
          />
        </div>
      </div>
    </div>
  )
}
