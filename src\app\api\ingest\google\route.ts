import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { GooglePhotosService, ReauthenticationRequiredError } from '@/lib/google-photos';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any;

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { pageToken, since } = await request.json();

    // Get the user's Google account tokens
    const account = await prisma.account.findFirst({
      where: {
        userId: session.user.id,
        provider: 'google',
      },
      select: {
        id: true,
        userId: true,
        provider: true,
        providerAccountId: true,
        access_token: true,
        refresh_token: true,
        expires_at: true,
        token_type: true,
        scope: true,
        id_token: true,
        session_state: true,
        requires_reauth: true
      }
    });
    
    console.log("Found Google account:", account);
    
    if (!account) {
      return NextResponse.json(
        { error: 'Google account not connected' },
        { status: 400 }
      );
    }
    
    if (!account.access_token) {
      return NextResponse.json(
        { error: 'Google access token missing' },
        { status: 400 }
      );
    }

    // Initialize Google Photos service with full account object
    const googlePhotos = new GooglePhotosService(account);

    // Fetch photos from Google Photos
    const response = await googlePhotos.getPhotos(pageToken, 100);

    if (response.mediaItems.length === 0) {
      return NextResponse.json({
        message: 'No more photos to fetch',
        nextPageToken: null,
        photosProcessed: 0,
      });
    }

    // Filter photos by date if 'since' is provided
    let photosToProcess = response.mediaItems;
    if (since) {
      const sinceDate = new Date(since);
      photosToProcess = response.mediaItems.filter(
        photo => new Date(photo.creationTime) > sinceDate
      );
    }

    // Save photos to database
    const photosProcessed = await googlePhotos.savePhotosToDatabase(
      session.user.id,
      photosToProcess
    );

    return NextResponse.json({
      message: `Successfully processed ${photosProcessed} photos`,
      nextPageToken: response.nextPageToken,
      photosProcessed,
    });

  } catch (error) {
    console.error('Error in Google Photos ingestion:', error);
    
    if (error instanceof ReauthenticationRequiredError) {
      return NextResponse.json(
        { error: 'REAUTH_REQUIRED' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch photos from Google Photos' },
      { status: 500 }
    );
  }
}
