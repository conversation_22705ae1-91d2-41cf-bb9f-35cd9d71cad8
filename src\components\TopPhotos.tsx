"use client"

import { useEffect, useState } from "react"
import { PhotoGrid } from "./PhotoGrid"
import { Button } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface Photo {
  id: string
  url: string
  thumbUrl: string
  takenAt: string | null
  quality: number | null
  significance: number | null
  cluster?: {
    kind: string
    label: string | null
  }
}

interface SignificanceScore {
  photoId: string
  totalScore: number
  breakdown: {
    quality: number
    people: number
    context: number
    subjectVariety: number
    social: number
    novelty: number
  }
}

interface TopPhotosProps {
  userId?: string
  onAnalysisComplete?: () => void
}

export function TopPhotos({ userId, onAnalysisComplete }: TopPhotosProps) {
  const [topPhotos, setTopPhotos] = useState<Photo[]>([])
  const [scoreBreakdowns, setScoreBreakdowns] = useState<Record<string, SignificanceScore['breakdown']>>({})
  const [loading, setLoading] = useState(false)
  const [analyzing, setAnalyzing] = useState(false)
  const [stats, setStats] = useState({
    totalPhotos: 0,
    clustersCreated: 0,
    memoriesGenerated: 0
  })

  useEffect(() => {
    if (userId) {
      loadTopPhotos()
    }
  }, [userId])

  const loadTopPhotos = async () => {
    try {
      setLoading(true)

      // Get top 10 photos with diversity constraints
      const scoresResponse = await fetch('/api/analyze/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ getTopPhotos: true }),
      })

      if (scoresResponse.ok) {
        const scoresData = await scoresResponse.json()
        const photoIds = scoresData.topPhotos?.map((score: SignificanceScore) => score.photoId) || []

        if (photoIds.length > 0) {
          // Get photo details
          const photosResponse = await fetch(`/api/photos?ids=${photoIds.join(',')}`)
          if (photosResponse.ok) {
            const photosData = await photosResponse.json()
            setTopPhotos(photosData.photos || [])

            // Create score breakdown mapping
            const breakdownMap: Record<string, SignificanceScore['breakdown']> = {}
            scoresData.topPhotos?.forEach((score: SignificanceScore) => {
              breakdownMap[score.photoId] = score.breakdown
            })
            setScoreBreakdowns(breakdownMap)
          }
        }

        setStats({
          totalPhotos: scoresData.stats?.totalPhotos || 0,
          clustersCreated: scoresData.stats?.clustersCreated || 0,
          memoriesGenerated: scoresData.stats?.memoriesGenerated || 0
        })
      }
    } catch (error) {
      console.error('Error loading top photos:', error)
    } finally {
      setLoading(false)
    }
  }

  const runAnalysis = async () => {
    try {
      setAnalyzing(true)

      const response = await fetch('/api/analyze/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ regenerate: false }),
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Analysis complete:', result)

        // Reload top photos after analysis
        await loadTopPhotos()

        if (onAnalysisComplete) {
          onAnalysisComplete()
        }
      } else {
        console.error('Analysis failed')
      }
    } catch (error) {
      console.error('Error during analysis:', error)
    } finally {
      setAnalyzing(false)
    }
  }

  const getScoreDistribution = () => {
    if (topPhotos.length === 0) return null

    const scores = topPhotos
      .map(photo => photo.significance || 0)
      .filter(score => score > 0)

    if (scores.length === 0) return null

    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
    const maxScore = Math.max(...scores)
    const minScore = Math.min(...scores)

    return { avgScore, maxScore, minScore }
  }

  const scoreDistribution = getScoreDistribution()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Your Top Photos</h2>
          <p className="text-gray-600">
            The most significant photos from your collection, selected with diversity in mind
          </p>
        </div>

        <Button
          onClick={runAnalysis}
          disabled={analyzing || loading}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {analyzing ? 'Analyzing...' : 'Re-run Analysis'}
        </Button>
      </div>

      {/* Stats Cards */}
      {stats.totalPhotos > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{stats.totalPhotos}</div>
              <div className="text-sm text-gray-600">Total Photos</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{topPhotos.length}</div>
              <div className="text-sm text-gray-600">Top Photos</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{stats.clustersCreated}</div>
              <div className="text-sm text-gray-600">Clusters Created</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">{stats.memoriesGenerated}</div>
              <div className="text-sm text-gray-600">Memories Generated</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Score Distribution */}
      {scoreDistribution && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Score Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {scoreDistribution.maxScore.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600">Highest Score</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {scoreDistribution.avgScore.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600">Average Score</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  {scoreDistribution.minScore.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600">Lowest Score</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Photo Grid */}
      <PhotoGrid
        photos={topPhotos}
        scoreBreakdowns={scoreBreakdowns}
        showFeedback={true}
        loading={loading}
        emptyMessage="No photos found. Upload some photos and run analysis to see your top photos!"
      />

      {/* Selection Criteria */}
      {topPhotos.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">How Photos Were Selected</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Diversity Constraints</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Maximum 3 photos from any single cluster</li>
                  <li>• Minimum Hamming distance of 10 between photos</li>
                  <li>• Balanced selection across time periods</li>
                  <li>• Prioritizes unique moments and compositions</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Scoring Factors</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• <strong>Quality (25%):</strong> Technical image quality</li>
                  <li>• <strong>People (20%):</strong> Face detection and social elements</li>
                  <li>• <strong>Context (20%):</strong> Temporal position in cluster</li>
                  <li>• <strong>Variety (15%):</strong> Uniqueness and composition</li>
                  <li>• <strong>Social (10%):</strong> Favorites and albums</li>
                  <li>• <strong>Novelty (10%):</strong> Burst detection and timing</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
