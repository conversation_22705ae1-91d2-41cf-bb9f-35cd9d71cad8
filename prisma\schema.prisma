// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// NextAuth.js models
model Account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String?
  access_token             String?
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String?
  session_state            String?
  refresh_token_expires_in Int?
  requires_reauth          <PERSON>olean @default(false)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts  Account[]
  sessions  Session[]
  photos    Photo[]
  clusters  Cluster[]
  memories  Memory[]
  settings  UserSettings?
  feedback  Feedback[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model UserSettings {
  id              String   @id @default(cuid())
  userId          String   @unique
  enableFaces     Boolean  @default(false)
  enableLocation  Boolean  @default(true)
  localOnly       Boolean  @default(false)
  homeGeohash     String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_settings")
}

model Photo {
  id           String     @id @default(cuid())
  userId       String
  source       String     // 'google' | 'upload'
  sourceId     String?    // Google Photos id
  url          String
  thumbUrl     String?
  takenAt      DateTime?
  lat          Float?
  lng          Float?
  geohash      String?

  @@index([geohash])
  width        Int?
  height       Int?
  exif         String?    // JSON string
  quality      Float?     // 0-100
  faces        Int?       // optional
  phash        String?
  clusterId    String?

  @@index([phash])
  @@index([clusterId])
  significance Float?     // 0-100
  createdAt    DateTime   @default(now())

  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  cluster   Cluster?  @relation(fields: [clusterId], references: [id])
  feedback  Feedback[]

  @@map("photos")
}

model Cluster {
  id        String   @id @default(cuid())
  userId    String

  @@index([userId])
  kind      String   // 'trip' | 'home'
  startAt   DateTime
  endAt     DateTime
  centerLat Float?
  centerLng Float?
  label     String?
  createdAt DateTime @default(now())

  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  photos Photo[]
  memory Memory?

  @@map("clusters")
}

model Memory {
  id          String   @id @default(cuid())
  userId      String
  clusterId   String   @unique

  @@index([userId])
  title       String
  summary     String
  coverPhotoId String
  startAt     DateTime
  endAt       DateTime
  createdAt   DateTime @default(now())

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  cluster Cluster @relation(fields: [clusterId], references: [id], onDelete: Cascade)

  @@map("memories")
}

model Feedback {
  id          String   @id @default(cuid())
  userId      String
  photoId     String

  @@index([userId])
  @@index([photoId])
  type        String   // 'upvote' | 'downvote'
  reason      String?  // Optional reason for feedback
  scoreBefore Float?   // Photo score before feedback
  scoreAfter  Float?   // Photo score after feedback
  createdAt   DateTime @default(now())

  user User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  photo Photo @relation(fields: [photoId], references: [id], onDelete: Cascade)

  @@map("feedback")
}
