import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any;

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const cursor = searchParams.get('cursor');
    const limit = parseInt(searchParams.get('limit') || '50');

    const photos = await prisma.photo.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        cluster: {
          select: {
            id: true,
            kind: true,
            label: true,
            startAt: true,
            endAt: true,
          },
        },
      },
      orderBy: {
        takenAt: 'desc',
      },
      take: limit + 1,
      cursor: cursor ? { id: cursor } : undefined,
    });

    const hasNextPage = photos.length > limit;
    const photosToReturn = hasNextPage ? photos.slice(0, -1) : photos;
    const nextCursor = hasNextPage ? photosToReturn[photosToReturn.length - 1]?.id : null;

    return NextResponse.json({
      photos: photosToReturn,
      nextCursor,
      hasNextPage,
    });

  } catch (error) {
    console.error('Error fetching photos:', error);
    return NextResponse.json(
      { error: 'Failed to fetch photos' },
      { status: 500 }
    );
  }
}
