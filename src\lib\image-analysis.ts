import sharp from 'sharp';
import { parse } from 'exifr';
// @ts-ignore - ngeohash doesn't have types
import { encode } from 'ngeohash';

export interface PhotoMetadata {
  width: number;
  height: number;
  aspectRatio: number;
  quality: number; // 0-100
  sharpness: number; // 0-100
  exposureBalance: number; // 0-100
  phash?: string;
  takenAt?: Date;
  lat?: number;
  lng?: number;
  geohash?: string;
  exif?: any;
}

export class ImageAnalysisService {
  async extractMetadata(buffer: ArrayBuffer): Promise<PhotoMetadata> {
    const uint8Array = new Uint8Array(buffer);

    // Extract EXIF data
    const exif = await parse(uint8Array, {
      tiff: true,
      xmp: true,
      icc: false,
      iptc: true,
      jfif: true,
      ihdr: true,
    });

    // Get basic image dimensions
    const sharpImage = sharp(uint8Array);
    const metadata = await sharpImage.metadata();

    // Extract location and time from EXIF
    const takenAt = exif?.DateTimeOriginal
      ? new Date(exif.DateTimeOriginal)
      : exif?.CreateDate
        ? new Date(exif.CreateDate)
        : undefined;

    const lat = exif?.latitude;
    const lng = exif?.longitude;
    const geohash = lat && lng ? encode(lat, lng, 7) : undefined;

    // Calculate aspect ratio
    const aspectRatio = metadata.width && metadata.height
      ? metadata.width / metadata.height
      : 1;

    // Calculate quality metrics
    const quality = await this.calculateQuality(sharpImage, metadata);
    const sharpness = await this.calculateSharpness(sharpImage, metadata);
    const exposureBalance = await this.calculateExposureBalance(sharpImage, metadata);

    return {
      width: metadata.width || 0,
      height: metadata.height || 0,
      aspectRatio,
      quality,
      sharpness,
      exposureBalance,
      takenAt,
      lat,
      lng,
      geohash,
      exif: exif ? JSON.stringify(exif) : undefined,
    };
  }

  private async calculateQuality(sharpImage: sharp.Sharp, metadata: sharp.Metadata): Promise<number> {
    // Simple quality score based on resolution and format
    let score = 50; // Base score

    // Resolution bonus (up to 25 points)
    if (metadata.width && metadata.height) {
      const megapixels = (metadata.width * metadata.height) / 1000000;
      score += Math.min(megapixels / 2, 25); // Cap at 25 points for 50MP
    }

    // Format quality bonus (up to 15 points)
    if (metadata.format === 'jpeg') {
      score += 12; // JPEG gets moderate bonus
    } else if (['heif', 'avif', 'webp'].includes(metadata.format || '')) {
      score += 15; // Modern formats get higher bonus
    }

    // Color space bonus (up to 10 points)
    if (metadata.space === 'srgb') {
      score += 10;
    } else if (['rgb16', 'lab'].includes(metadata.space || '')) {
      score += 8;
    }

    return Math.min(Math.round(score), 100);
  }

  private async calculateSharpness(sharpImage: sharp.Sharp, metadata: sharp.Metadata): Promise<number> {
    try {
      // Convert to grayscale and resize for faster processing
      const resized = sharpImage
        .grayscale()
        .resize(400, 400, { fit: 'inside', withoutEnlargement: true });

      const { data, info } = await resized.raw().toBuffer({ resolveWithObject: true });

      // Simple Laplacian variance calculation for sharpness
      let variance = 0;
      const kernel = [-1, -1, -1, -1, 8, -1, -1, -1, -1];
      const width = info.width;
      const height = info.height;

      for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
          let sum = 0;
          for (let ky = -1; ky <= 1; ky++) {
            for (let kx = -1; kx <= 1; kx++) {
              const pixel = data[(y + ky) * width + (x + kx)];
              sum += pixel * kernel[(ky + 1) * 3 + (kx + 1)];
            }
          }
          variance += sum * sum;
        }
      }

      const normalizedVariance = variance / (width * height);
      // Normalize to 0-100 scale (rough heuristic)
      const sharpness = Math.min(Math.max(normalizedVariance / 1000, 0), 100);

      return Math.round(sharpness);
    } catch (error) {
      console.error('Error calculating sharpness:', error);
      return 50; // Default to medium sharpness
    }
  }

  private async calculateExposureBalance(sharpImage: sharp.Sharp, metadata: sharp.Metadata): Promise<number> {
    try {
      // Get histogram data
      const histogram = await sharpImage
        .grayscale()
        .resize(200, 200, { fit: 'inside', withoutEnlargement: true })
        .raw()
        .toBuffer();

      // Calculate histogram
      const hist = new Array(256).fill(0);
      for (let i = 0; i < histogram.length; i++) {
        hist[histogram[i]]++;
      }

      // Calculate statistics
      const total = histogram.length;
      let mean = 0;
      let variance = 0;

      for (let i = 0; i < 256; i++) {
        mean += (hist[i] / total) * i;
      }

      for (let i = 0; i < 256; i++) {
        variance += (hist[i] / total) * Math.pow(i - mean, 2);
      }

      // Exposure balance is based on how well distributed the histogram is
      // and how close the mean is to the middle (128)
      const distribution = Math.sqrt(variance) / 128; // Normalized std dev
      const centerBias = 1 - Math.abs(mean - 128) / 128;

      const balance = (distribution * 0.6 + centerBias * 0.4) * 100;
      return Math.round(Math.max(0, Math.min(100, balance)));
    } catch (error) {
      console.error('Error calculating exposure balance:', error);
      return 50; // Default to neutral
    }
  }

  // Simple perceptual hash implementation (dHash variant)
  async calculatePHash(buffer: ArrayBuffer): Promise<string> {
    try {
      const uint8Array = new Uint8Array(buffer);
      const resized = await sharp(uint8Array)
        .grayscale()
        .resize(9, 8, { fit: 'fill' })
        .raw()
        .toBuffer();

      let hash = '';
      for (let y = 0; y < 8; y++) {
        for (let x = 0; x < 8; x++) {
          const left = resized[y * 9 + x];
          const right = resized[y * 9 + x + 1];
          hash += left < right ? '0' : '1';
        }
      }

      return parseInt(hash, 2).toString(16).padStart(16, '0');
    } catch (error) {
      console.error('Error calculating pHash:', error);
      return '0'.repeat(16);
    }
  }
}
