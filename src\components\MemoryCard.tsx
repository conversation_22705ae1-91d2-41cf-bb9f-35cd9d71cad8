"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface Memory {
  id: string
  title: string
  summary: string
  startAt: string
  endAt: string
  clusterId: string
  coverPhotoId: string
  coverPhoto?: {
    url: string
    thumbUrl: string
    takenAt: string | null
  }
  cluster?: {
    kind: string
    label: string | null
    centerLat: number | null
    centerLng: number | null
  }
  photos?: Array<{
    id: string
    url: string
    thumbUrl: string
    takenAt: string | null
  }>
}

interface MemoryCardProps {
  memory: Memory
  showPhotos?: boolean
  maxPhotos?: number
}

export function MemoryCard({ memory, showPhotos = false, maxPhotos = 4 }: MemoryCardProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  const handleImageError = () => {
    setImageError(true)
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return start.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    }

    return `${start.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })} - ${end.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })}`
  }

  const getMemoryIcon = (kind: string) => {
    return kind === 'trip' ? '🏕️' : '🏠'
  }

  const getLocationText = (memory: Memory) => {
    if (memory.cluster?.label) {
      return memory.cluster.label
    }
    if (memory.cluster?.centerLat && memory.cluster?.centerLng) {
      return `${memory.cluster.centerLat.toFixed(2)}, ${memory.cluster.centerLng.toFixed(2)}`
    }
    return 'Unknown Location'
  }

  return (
    <Link href={`/memories/${memory.id}`}>
      <Card className="group relative overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer bg-white border-0 shadow-lg hover:-translate-y-1">
        {/* Cover Photo */}
        <div className="relative h-48 overflow-hidden">
          {!imageError && memory.coverPhoto ? (
            <img
              src={memory.coverPhoto.thumbUrl || memory.coverPhoto.url}
              alt={memory.title}
              className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
                !imageLoaded ? 'opacity-0' : 'opacity-100'
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
              <svg className="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
          )}

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

          {/* Memory type badge */}
          <div className="absolute top-3 left-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1">
            <span className="text-sm font-medium text-gray-900">
              {getMemoryIcon(memory.cluster?.kind || 'home')} {memory.cluster?.kind === 'trip' ? 'Trip' : 'Home'}
            </span>
          </div>

          {/* Photo count badge */}
          {memory.photos && memory.photos.length > 1 && (
            <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1">
              <span className="text-sm font-medium text-gray-900">
                📸 {memory.photos.length}
              </span>
            </div>
          )}
        </div>

        <CardContent className="p-6">
          {/* Title */}
          <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {memory.title}
          </h3>

          {/* Date Range */}
          <div className="flex items-center text-sm text-gray-600 mb-3">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            {formatDateRange(memory.startAt, memory.endAt)}
          </div>

          {/* Location */}
          <div className="flex items-center text-sm text-gray-600 mb-4">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
            <span className="truncate">{getLocationText(memory)}</span>
          </div>

          {/* Summary */}
          <p className="text-gray-700 text-sm leading-relaxed line-clamp-3 mb-4">
            {memory.summary}
          </p>

          {/* Photo thumbnails (optional) */}
          {showPhotos && memory.photos && memory.photos.length > 1 && (
            <div className="flex space-x-2 mb-4">
              {memory.photos.slice(0, maxPhotos).map((photo, index) => (
                <div
                  key={photo.id}
                  className="w-12 h-12 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0"
                >
                  <img
                    src={photo.thumbUrl || photo.url}
                    alt=""
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                </div>
              ))}
              {memory.photos.length > maxPhotos && (
                <div className="w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center text-xs font-medium text-gray-600">
                  +{memory.photos.length - maxPhotos}
                </div>
              )}
            </div>
          )}

          {/* View Memory Button */}
          <Button
            variant="outline"
            className="w-full group-hover:bg-blue-50 group-hover:border-blue-300 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
              <path d="M5 4a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V7a1 1 0 00-2 0v5H5V6h5a1 1 0 000-2H5z" />
            </svg>
            View Memory
          </Button>
        </CardContent>
      </Card>
    </Link>
  )
}
