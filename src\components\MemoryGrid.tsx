"use client"

import { MemoryCard } from "./MemoryCard"

interface Memory {
  id: string
  title: string
  summary: string
  startAt: string
  endAt: string
  clusterId: string
  coverPhotoId: string
  coverPhoto?: {
    url: string
    thumbUrl: string
    takenAt: string | null
  }
  cluster?: {
    kind: string
    label: string | null
    centerLat: number | null
    centerLng: number | null
  }
  photos?: Array<{
    id: string
    url: string
    thumbUrl: string
    takenAt: string | null
  }>
}

interface MemoryGridProps {
  memories: Memory[]
  loading?: boolean
  showPhotos?: boolean
  emptyMessage?: string
}

export function MemoryGrid({
  memories,
  loading = false,
  showPhotos = false,
  emptyMessage = "No memories found yet. Upload some photos and run analysis to create memories!"
}: MemoryGridProps) {
  if (loading && memories.length === 0) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-lg overflow-hidden animate-pulse">
            <div className="h-48 bg-gray-200"></div>
            <div className="p-6 space-y-3">
              <div className="h-6 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              <div className="h-16 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (memories.length === 0) {
    return (
      <div className="text-center py-12">
        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
        </svg>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No memories yet</h3>
        <p className="text-gray-500 max-w-md mx-auto">{emptyMessage}</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {memories.map((memory) => (
        <MemoryCard
          key={memory.id}
          memory={memory}
          showPhotos={showPhotos}
        />
      ))}

      {loading && memories.length > 0 && (
        <div className="bg-white rounded-lg shadow-lg overflow-hidden animate-pulse">
          <div className="h-48 bg-gray-200"></div>
          <div className="p-6 space-y-3">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            <div className="h-16 bg-gray-200 rounded"></div>
          </div>
        </div>
      )}
    </div>
  )
}
