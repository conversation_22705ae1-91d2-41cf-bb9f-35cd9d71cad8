"use client"

import { signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/useToast"

export function ConnectCard() {
  const router = useRouter()
  const [isConnecting, setIsConnecting] = useState(false)
  const { addToast } = useToast()

  const handleConnect = async () => {
    setIsConnecting(true)
    try {
      addToast("info", "Connecting", "Authenticating with Google...")

      // First, do the OAuth flow
      const result = await signIn("google", {
        callbackUrl: "/dashboard",
        redirect: false
      })

      if (result?.ok) {
        addToast("success", "Connected", "Successfully connected to Google!")

        // If OAuth was successful, try to import photos
        try {
          addToast("info", "Importing", "Starting to import your photos...")

          const response = await fetch('/api/ingest/google', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ pageToken: null }),
          })

          if (response.ok) {
            const data = await response.json()
            addToast("success", "Import Started", `Successfully started importing ${data.photosProcessed || 0} photos!`)
            // Successfully started importing photos, redirect to dashboard
            router.push('/dashboard')
          } else {
            const errorData = await response.json().catch(() => ({}))
            addToast("error", "Import Failed", errorData.error || "Failed to import photos")
            // Import failed, still redirect to dashboard
            router.push('/dashboard')
          }
        } catch (error) {
          console.error('Error importing photos:', error)
          addToast("error", "Import Error", "Failed to start photo import")
          // Still redirect even if import fails
          router.push('/dashboard')
        }
      } else {
        addToast("error", "Connection Failed", "Failed to connect to Google")
      }
    } catch (error) {
      console.error('Error connecting to Google:', error)
      addToast("error", "Connection Error", "An unexpected error occurred")
    } finally {
      setIsConnecting(false)
    }
  }

  return (
    <Card className="h-full group hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-500 bg-white/90 backdrop-blur-sm border-0 shadow-xl relative overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      <CardHeader className="text-center pb-8 relative z-10">
        <div className="relative">
          <div className="w-24 h-24 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-3xl flex items-center justify-center mb-8 mx-auto shadow-xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-500">
            <svg className="w-12 h-12 text-white group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A.5.5 0 0014 8v4a2 2 0 002 2h2a2 2 0 002-2V9a.5.5 0 00-.553-.894l-2 1z" />
            </svg>
          </div>
          {/* Floating accent elements */}
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full animate-ping"></div>
          <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-gradient-to-br from-pink-400 to-rose-500 rounded-full animate-pulse delay-300"></div>
        </div>
        <CardTitle className="text-3xl font-bold text-gray-900 mb-4 group-hover:text-blue-900 transition-colors duration-300">
          Connect Google Photos
        </CardTitle>
        <CardDescription className="text-lg text-gray-600 leading-relaxed">
          Import your photos directly from Google Photos with one click and unlock AI-powered memories
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center space-y-8 px-8 pb-10 relative z-10">
        <Button
          onClick={handleConnect}
          disabled={isConnecting}
          className="w-full bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:via-gray-500 disabled:to-gray-600 text-white font-semibold py-5 px-8 rounded-2xl shadow-xl hover:shadow-2xl disabled:shadow-none transition-all duration-300 transform hover:scale-[1.02] hover:-translate-y-1 disabled:transform-none relative overflow-hidden group/button"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover/button:translate-x-[100%] transition-transform duration-700"></div>
          {isConnecting ? (
            <>
              <div className="w-6 h-6 mr-3 relative z-10 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
              <span className="relative z-10 text-lg">Connecting...</span>
            </>
          ) : (
            <>
              <svg className="w-6 h-6 mr-3 relative z-10" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span className="relative z-10 text-lg">Connect Google Photos</span>
            </>
          )}
        </Button>
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 w-full border border-blue-100">
          <p className="text-sm text-blue-700 text-center font-medium leading-relaxed">
            🔒 <strong>Privacy First:</strong> We only read your photos and metadata. Your images stay safe in your Google account.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
